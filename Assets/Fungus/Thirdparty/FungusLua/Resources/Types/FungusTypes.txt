﻿{
	"registerTypes" : [
		"Fungus.AnimatorVariable",
		"Fungus.AudioSourceVariable",
		"Fungus.Block",
		"Fungus.BooleanVariable",
		"Fungus.Character",
		"Fungus.ColorVariable",
		"Fungus.Command",
		"Fungus.CommandInfoAttribute",
		"Fungus.FacingDirection",
		"Fungus.FloatVariable",
		"Fungus.Flowchart",
		"Fungus.FungusPrefs",
		"Fungus.LuaEnvironment",
		"Fungus.LuaUtils",
		"Fungus.GameObjectVariable",
		"Fungus.IntegerVariable",
		"Fungus.MaterialVariable",
		"Fungus.MenuDialog",
		"Fungus.ObjectVariable",
		"Fungus.PODTypeFactory",
		"Fungus.PortraitState",
		"Fungus.SayDialog",
		"Fungus.SpriteVariable",
		"Fungus.StringVariable",
		"Fungus.Task",
		"Fungus.TextureVariable",
		"Fungus.TransformVariable",
		"Fungus.Variable",
		"Fungus.Vector2Variable",
		"Fungus.Vector3Variable",
        "Fungus.Label"
	],
	"extensionTypes" : [
	]
}