﻿{
	"registerTypes" : [
		"System.Action, System.Core, Version=3.5.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089",
		"System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089",
		"System.Collections.IEnumerator, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089",
		"System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089",
		"System.IntPtr, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089",
		"System.Single, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089",
		"System.Type, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089",
		"System.UInt32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089",
		"UnityEngine.AudioClip, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.AudioClipLoadType, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.AudioDataLoadState, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.AudioSource, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Bounds, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Canvas, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Color, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Component, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.ComputeBuffer, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Coroutine, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.EventSystems.BaseEventData, UnityEngine.UI, Version=*******, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.EventSystems.PointerEventData, UnityEngine.UI, Version=*******, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Font, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.FontStyle, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.GameObject, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.HorizontalWrapMode, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Material, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.MaterialGlobalIlluminationFlags, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Matrix4x4, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.MonoBehaviour, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Object, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.PlayerPrefs, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.PrimitiveType, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Quaternion, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Rect, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.RectTransform, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.SceneManagement.Scene, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.ScriptableObject, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.SendMessageOptions, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Shader, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Sprite, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.SpriteMeshType, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.SpritePackingMode, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.SpritePackingRotation, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.TextAnchor, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.TextGenerationSettings, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.TextGenerator, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Texture, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Texture2D, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.TextureFormat, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Time, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Transform, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.UI.Button, UnityEngine.UI, Version=*******, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.UI.Image, UnityEngine.UI, Version=*******, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.UI.Slider, UnityEngine.UI, Version=*******, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.UI.Text, UnityEngine.UI, Version=*******, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Vector2, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Vector3, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.Vector4, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null",
		"UnityEngine.VerticalWrapMode, UnityEngine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null"
	],
	"extensionTypes" : [
	]
}
