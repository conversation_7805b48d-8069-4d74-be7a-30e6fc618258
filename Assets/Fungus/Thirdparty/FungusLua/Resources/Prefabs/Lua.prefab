%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &179118
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 466848}
  - 114: {fileID: 11415008}
  - 114: {fileID: 11417560}
  - 114: {fileID: 11499092}
  - 114: {fileID: 11483650}
  - 114: {fileID: 11437776}
  m_Layer: 0
  m_Name: Lua
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &466848
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 179118}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!114 &11415008
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 179118}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6ee79785811ba49399c1b56d7309e3df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  executeAfterTime: 1
  repeatExecuteTime: 1
  repeatEveryTime: 1
  executeAfterFrames: 1
  repeatExecuteFrame: 1
  repeatEveryFrame: 1
  hasFailed: 0
  executeMethods: 2
  executeMethodName: OnExecute
--- !u!114 &11417560
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 179118}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 446caeace65234baaacd52095d24f101, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  luaEnvironment: {fileID: 11483650}
  luaFile: {fileID: 0}
  luaScript: 
  runAsCoroutine: 1
--- !u!114 &11437776
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 179118}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c10f0b861365b42b0928858f7b086ff3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fungusModule: 0
  activeLanguage: en
  stringTables: []
  registerTypes:
  - {fileID: 4900000, guid: 9c3ab7a98d51241bbb499643399fa761, type: 3}
  - {fileID: 4900000, guid: 93fddea8208764a2dbb189cc238aed40, type: 3}
--- !u!114 &11483650
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 179118}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ba19c26c1ba7243d2b57ebc4329cc7c6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  remoteDebugger: 0
--- !u!114 &11499092
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 179118}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4cc8a659e950044b69d7c62696c36962, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  allEnvironments: 1
  luaEnvironment: {fileID: 11483650}
  tableName: 
  registerTypes: 1
  boundTypes: []
  boundObjects:
  - key: 
    obj: {fileID: 0}
    component: {fileID: 0}
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications: []
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 179118}
  m_IsPrefabParent: 1
