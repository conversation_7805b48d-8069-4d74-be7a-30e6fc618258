// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

//Adapted from http://wiki.unity3d.com/index.php/EnumFlagPropertyDrawer
//placed in fungus namespace to avoid collisions with your own

using UnityEngine;

namespace Fungus
{
    public class EnumFlagAttribute : PropertyAttribute
    {
        public string enumName;

        public EnumFlagAttribute() { }

        public EnumFlagAttribute(string name)
        {
            enumName = name;
        }
    }
}