// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

/*This script has been, partially or completely, generated by the Fungus.GenerateVariableWindow*/

using UnityEngine;

namespace Fungus
{
    /// <summary>
    /// Quaternion variable type.
    /// </summary>
    [VariableInfo("Other", "Quaternion")]
    [AddComponentMenu("")]
    [System.Serializable]
    public class QuaternionVariable : VariableBase<UnityEngine.Quaternion>
    {
        public override bool IsArithmeticSupported(SetOperator setOperator)
        {
            return setOperator == SetOperator.Multiply || base.IsArithmeticSupported(setOperator);
        }

        public override void Apply(SetOperator setOperator, Quaternion value)
        {
            Quaternion local = Value;

            switch (setOperator)
            {
                case SetOperator.Add:
                    Debug.LogWarning("Quarternion Add not supported");
                    break;

                case SetOperator.Subtract:
                    Debug.LogWarning("Quarternion Subtract not supported");
                    break;

                case SetOperator.Multiply:
                    Value = local * value;
                    break;

                case SetOperator.Divide:
                    Debug.LogWarning("Quarternion Divide not supported");
                    break;

                default:
                base.Apply(setOperator, value);
                break;
            }
        }
    }

    /// <summary>
    /// Container for a Quaternion variable reference or constant value.
    /// </summary>
    [System.Serializable]
    public struct QuaternionData
    {
        [SerializeField]
        [VariableProperty("<Value>", typeof(QuaternionVariable))]
        public QuaternionVariable quaternionRef;

        [SerializeField]
        public UnityEngine.Quaternion quaternionVal;

        public static implicit operator UnityEngine.Quaternion(QuaternionData QuaternionData)
        {
            return QuaternionData.Value;
        }

        public QuaternionData(UnityEngine.Quaternion v)
        {
            quaternionVal = v;
            quaternionRef = null;
        }

        public UnityEngine.Quaternion Value
        {
            get { return (quaternionRef == null) ? quaternionVal : quaternionRef.Value; }
            set { if (quaternionRef == null) { quaternionVal = value; } else { quaternionRef.Value = value; } }
        }

        public string GetDescription()
        {
            if (quaternionRef == null)
            {
                return quaternionVal.ToString();
            }
            else
            {
                return quaternionRef.Key;
            }
        }
    }
}