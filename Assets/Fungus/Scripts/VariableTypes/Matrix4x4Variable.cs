// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

/*This script has been, partially or completely, generated by the Fungus.GenerateVariableWindow*/

using UnityEngine;

namespace Fungus
{
    /// <summary>
    /// Matrix4x4 variable type.
    /// </summary>
    [VariableInfo("Other", "Matrix4x4", IsPreviewedOnly = true)]
    [AddComponentMenu("")]
    [System.Serializable]
    public class Matrix4x4Variable : VariableBase<UnityEngine.Matrix4x4>
    {
        public override bool IsArithmeticSupported(SetOperator setOperator)
        {
            return setOperator == SetOperator.Multiply || base.IsArithmeticSupported(setOperator);
        }

        public override void Apply(SetOperator setOperator, Matrix4x4 value)
        {
            Matrix4x4 local = Value;

            switch (setOperator)
            {
                case SetOperator.Add:
                    Debug.LogWarning("Matrix4x4 Add not supported");
                    break;

                case SetOperator.Subtract:
                    Debug.LogWarning("Matrix4x4 Subtract not supported");
                    break;

                case SetOperator.Multiply:
                    Value = local * value;
                    break;

                case SetOperator.Divide:
                    Debug.LogWarning("Matrix4x4 Divide not supported");
                    break;

                default:
                base.Apply(setOperator, value);
                break;
            }
        }
    }

    /// <summary>
    /// Container for a Matrix4x4 variable reference or constant value.
    /// </summary>
    [System.Serializable]
    public struct Matrix4x4Data
    {
        [SerializeField]
        [VariableProperty("<Value>", typeof(Matrix4x4Variable))]
        public Matrix4x4Variable matrix4x4Ref;

        [SerializeField]
        public UnityEngine.Matrix4x4 matrix4x4Val;

        public static implicit operator UnityEngine.Matrix4x4(Matrix4x4Data Matrix4x4Data)
        {
            return Matrix4x4Data.Value;
        }

        public Matrix4x4Data(UnityEngine.Matrix4x4 v)
        {
            matrix4x4Val = v;
            matrix4x4Ref = null;
        }

        public UnityEngine.Matrix4x4 Value
        {
            get { return (matrix4x4Ref == null) ? matrix4x4Val : matrix4x4Ref.Value; }
            set { if (matrix4x4Ref == null) { matrix4x4Val = value; } else { matrix4x4Ref.Value = value; } }
        }

        public string GetDescription()
        {
            if (matrix4x4Ref == null)
            {
                return matrix4x4Val.ToString();
            }
            else
            {
                return matrix4x4Ref.Key;
            }
        }
    }
}