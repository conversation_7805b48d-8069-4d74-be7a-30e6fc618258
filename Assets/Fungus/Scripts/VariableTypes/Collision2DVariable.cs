// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

/*This script has been, partially or completely, generated by the Fungus.GenerateVariableWindow*/

using UnityEngine;

namespace Fungus
{
    /// <summary>
    /// Collision2D variable type.
    /// </summary>
    [VariableInfo("Other", "Collision2D", IsPreviewedOnly = true)]
    [AddComponentMenu("")]
    [System.Serializable]
    public class Collision2DVariable : VariableBase<UnityEngine.Collision2D>
    { }
}